const dbConnection = require('./connection');

/**
 * Check database contents and display summary
 */
async function checkDatabase() {
    try {
        console.log('Checking database contents...\n');
        
        // Connect to database
        await dbConnection.connect();
        
        // Check users
        const users = await dbConnection.all('SELECT user_id, username, role, full_name FROM users ORDER BY created_at');
        console.log('=== USERS ===');
        console.log(`Total users: ${users.length}`);
        users.forEach(user => {
            console.log(`- ${user.username} (${user.role}): ${user.full_name}`);
        });
        console.log();
        
        // Check students
        const students = await dbConnection.all('SELECT student_id, lrn, first_name, last_name, grade_level, section FROM students ORDER BY last_name, first_name');
        console.log('=== STUDENTS ===');
        console.log(`Total students: ${students.length}`);
        students.forEach(student => {
            console.log(`- ${student.lrn}: ${student.first_name} ${student.last_name} (${student.grade_level} - ${student.section})`);
        });
        console.log();
        
        // Check subjects
        const subjects = await dbConnection.all(`
            SELECT s.subject_id, s.subject_code, s.subject_name, s.grade_level, u.full_name as teacher_name 
            FROM subjects s 
            JOIN users u ON s.teacher_id = u.user_id 
            ORDER BY s.subject_code
        `);
        console.log('=== SUBJECTS ===');
        console.log(`Total subjects: ${subjects.length}`);
        subjects.forEach(subject => {
            console.log(`- ${subject.subject_code}: ${subject.subject_name} (${subject.grade_level}) - Teacher: ${subject.teacher_name}`);
        });
        console.log();
        
        // Check class sessions
        const sessions = await dbConnection.all('SELECT COUNT(*) as count FROM class_sessions');
        console.log('=== CLASS SESSIONS ===');
        console.log(`Total sessions: ${sessions[0].count}`);
        console.log();
        
        // Check attendance records
        const attendance = await dbConnection.all('SELECT COUNT(*) as count FROM attendance');
        console.log('=== ATTENDANCE RECORDS ===');
        console.log(`Total attendance records: ${attendance[0].count}`);
        console.log();
        
        // Check SMS logs
        const smsLogs = await dbConnection.all('SELECT COUNT(*) as count FROM sms_logs');
        console.log('=== SMS LOGS ===');
        console.log(`Total SMS logs: ${smsLogs[0].count}`);
        console.log();
        
        console.log('Database check completed successfully!');
        
    } catch (error) {
        console.error('Database check failed:', error.message);
        process.exit(1);
    } finally {
        await dbConnection.close();
    }
}

// Run check if this file is executed directly
if (require.main === module) {
    checkDatabase();
}

module.exports = {
    checkDatabase
};
