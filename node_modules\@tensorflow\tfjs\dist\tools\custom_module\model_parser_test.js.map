{"version": 3, "file": "model_parser_test.js", "sourceRoot": "", "sources": ["../../../tools/custom_module/model_parser_test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,iDAAiD;AACjD,8EAAgF;AAEhF,uBAAyB;AAEzB,+CAAsC;AAEtC,IAAM,YAAY,GAAsB;IACtC,MAAM,EAAE,aAAa;IACrB,WAAW,EAAE,OAAO;IACpB,WAAW,EAAE,WAAW;IACxB,aAAa,EAAE;QACb,IAAI,EAAE;YACJ;gBACE,IAAI,EAAE,OAAO;gBACb,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE;oBACJ,KAAK,EAAE;wBACL,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ;qBACnC;oBACD,KAAK,EAAE,EAAC,KAAK,EAAE,EAAC,GAAG,EAAE,CAAC,EAAC,IAAI,EAAE,CAAC,CAAC,EAAC,EAAE,EAAC,IAAI,EAAE,CAAC,EAAC,CAAC,EAAC,EAAC;iBAC/C;aACF;YACD,EAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,EAAC;YAC9D,EAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,EAAC;SAC7D;QACD,QAAQ,EAAE,EAAC,QAAQ,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,EAAC;KAC1C;CACF,CAAC;AAEF,IAAM,qBAAqB,GAAsB;IAC/C,MAAM,EAAE,aAAa;IACrB,WAAW,EAAE,OAAO;IACpB,WAAW,EAAE,WAAW;IACxB,aAAa,EAAE;QACb,IAAI,EAAE;YACJ;gBACE,IAAI,EAAE,mBAAmB;gBACzB,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE;oBACJ,KAAK,EAAE;wBACL,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ;qBACnC;oBACD,KAAK,EACD,EAAC,KAAK,EAAE,EAAC,GAAG,EAAE,CAAC,EAAC,IAAI,EAAE,GAAG,EAAC,EAAE,EAAC,IAAI,EAAE,CAAC,EAAC,EAAE,EAAC,IAAI,EAAE,GAAG,EAAC,EAAE,EAAC,IAAI,EAAE,CAAC,EAAC,CAAC,EAAC,EAAC;iBACrE;aACF;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE;oBACJ,KAAK,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;oBAC3C,KAAK,EAAE;wBACL,MAAM,EAAE;4BACN,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ;4BACnC,WAAW,EAAE,EAAC,GAAG,EAAE,CAAC,EAAC,IAAI,EAAE,CAAC,EAAC,EAAE,EAAC,IAAI,EAAE,CAAC,EAAC,EAAE,EAAC,IAAI,EAAE,CAAC,EAAC,EAAE,EAAC,IAAI,EAAE,CAAC,EAAC,CAAC,EAAC;4BAChE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;yBACpC;qBACF;iBACF;aACF;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE;oBACJ,KAAK,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;oBAC3C,KAAK,EAAE;wBACL,MAAM,EAAE;4BACN,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ;4BACnC,WAAW,EAAE,EAAC,GAAG,EAAE,CAAC,EAAC,IAAI,EAAE,CAAC,EAAC,EAAE,EAAC,IAAI,EAAE,CAAC,EAAC,EAAE,EAAC,IAAI,EAAE,CAAC,EAAC,EAAE,EAAC,IAAI,EAAE,CAAC,EAAC,CAAC,EAAC;4BAChE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;yBAClB;qBACF;iBACF;aACF;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,EAAC,KAAK,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC,EAAE,KAAK,EAAE,EAAC,CAAC,EAAE,CAAC,EAAC,EAAC;aACnE;YACD,EAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,EAAC;YAC/D;gBACE,IAAI,EAAE,QAAQ;gBACd,EAAE,EAAE,QAAQ;gBACZ,KAAK,EAAE,CAAC,mBAAmB,EAAE,OAAO,CAAC;gBACrC,IAAI,EAAE;oBACJ,CAAC,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;oBACvC,UAAU,EAAE,EAAC,CAAC,EAAE,UAAU,EAAC;oBAC3B,OAAO,EAAE,EAAC,CAAC,EAAE,UAAU,EAAC;oBACxB,OAAO,EAAE,EAAC,IAAI,EAAE,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC,EAAC;oBACzC,aAAa,EAAE,EAAC,CAAC,EAAE,IAAI,EAAC;iBACzB;aACF;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,EAAE,EAAE,SAAS;gBACb,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;gBAC1B,IAAI,EAAE;oBACJ,CAAC,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;oBACvC,UAAU,EAAE,EAAC,CAAC,EAAE,UAAU,EAAC;iBAC5B;aACF;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,EAAE,EAAE,MAAM;gBACV,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,IAAI,EAAE,EAAC,IAAI,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC,EAAC;aACnD;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,EAAE,EAAE,SAAS;gBACb,KAAK,EAAE,CAAC,MAAM,CAAC;gBACf,IAAI,EAAE,EAAC,YAAY,EAAE,EAAC,IAAI,EAAE,EAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAC,EAAC,EAAC;aAC9C;YACD;gBACE,IAAI,EAAE,UAAU;gBAChB,EAAE,EAAE,SAAS;gBACb,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,IAAI,EAAE,EAAC,YAAY,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC,EAAC;aACjC;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,EAAE,EAAE,OAAO;gBACX,KAAK,EAAE,CAAC,mBAAmB,CAAC;gBAC5B,IAAI,EAAE,EAAC,SAAS,EAAE,EAAC,CAAC,EAAE,CAAC,EAA0B,EAAC;aACnD;YACD,EAAC,IAAI,EAAE,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,mBAAmB,CAAC,EAAC;YACpE;gBACE,IAAI,EAAE,gBAAgB;gBACtB,EAAE,EAAE,gBAAgB;gBACpB,KAAK,EAAE,CAAC,mBAAmB,CAAC;gBAC5B,IAAI,EAAE,EAAC,OAAO,EAAE,EAAC,CAAC,EAAE,MAAM,EAA0B,EAAC;aACtD;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,EAAE,EAAE,MAAM;gBACV,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,IAAI,EAAE,EAAC,IAAI,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC,EAAC;aACnD;SACF;QACD,OAAO,EAAE;YACP,QAAQ,EAAE;gBACR;oBACE,SAAS,EAAE;wBACT,IAAI,EAAE,qCAAqC;wBAC3C,QAAQ,EAAE;4BACR,EAAC,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;4BAChE;gCACE,IAAI,EAAE,0BAA0B;gCAChC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ;6BACnC;4BACD,EAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;4BACzD,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC,EAAE;gCACpD,IAAI,EAAE,wCAAwC;gCAC9C,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ;6BACnC;yBACF;wBACD,SAAS,EAAE,CAAC,EAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAC,CAAC;qBACnE;oBACD,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,EAAE,EAAE,MAAM;4BACV,KAAK,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC;4BAChC,IAAI,EAAE,EAAC,CAAC,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC,EAAC;yBAChD,CAAC;oBACF,GAAG,EAAE,EAAC,QAAQ,EAAE,UAAU,EAAC;iBAC5B;gBACD;oBACE,SAAS,EAAE;wBACT,IAAI,EAAE,qCAAqC;wBAC3C,QAAQ,EAAE;4BACR,EAAC,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;4BAChE;gCACE,IAAI,EAAE,0BAA0B;gCAChC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ;6BACnC;4BACD,EAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;4BACzD,EAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;4BACjD,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;yBACxD;wBACD,SAAS,EAAE;4BACT,EAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;4BACtD,EAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;4BACxD,EAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;4BACxD,EAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;4BAC/C,EAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;yBACtD;qBACF;oBACD,OAAO,EAAE;wBACP;4BACE,IAAI,EAAE,SAAS;4BACf,EAAE,EAAE,OAAO;4BACX,IAAI,EAAE;gCACJ,KAAK,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC;gCAC3C,KAAK,EAAE;oCACL,MAAM,EAAE;wCACN,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ;wCACnC,WAAW,EAAE,EAAE;wCACf,MAAM,EAAE,CAAC,CAAC,CAAC;qCACZ;iCACF;6BACF;yBACF;wBACD;4BACE,IAAI,EAAE,KAAK;4BACX,EAAE,EAAE,OAAO;4BACX,KAAK,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC;4BAC7B,IAAI,EAAE,EAAC,CAAC,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC,EAAC;yBAChD;wBACD;4BACE,IAAI,EAAE,OAAO;4BACb,EAAE,EAAE,OAAO;4BACX,KAAK,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;4BACjD,IAAI,EAAE,EAAC,CAAC,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC,EAAC;yBAChD;wBACD;4BACE,IAAI,EAAE,OAAO;4BACb,EAAE,EAAE,OAAO;4BACX,KAAK,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;4BAC/B,IAAI,EAAE,EAAC,CAAC,EAAE,EAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAC,EAAC;yBAChD;qBACF;oBACD,GAAG,EAAE;wBACH,UAAU,EAAE,0BAA0B;wBACtC,UAAU,EAAE,WAAW;wBACvB,CAAC,EAAE,KAAK;wBACR,QAAQ,EAAE,WAAW;wBACrB,OAAO,EAAE,WAAW;qBACrB;iBACF;aACF;SACF;QACD,QAAQ,EAAE,EAAC,QAAQ,EAAE,GAAG,EAAC;KAC1B;CACF,CAAC;AAEF,QAAQ,CAAC,aAAa,EAAE;IACtB,mCAAmC;IACnC,IAAI,WAAgB,CAAC;IACrB,SAAS,CAAC;QACR,IAAM,WAAW,GACb,OAAO,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;QAC1E,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE;QACrC,IAAM,GAAG,GAAG,IAAA,qBAAM,EAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QAC9C,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2CAA2C,EAAE;QAC9C,IAAM,GAAG,GAAG,IAAA,qBAAM,EAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;QACvD,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC;YACjD,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ;YACtE,MAAM,EAAE,MAAM;SACf,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}