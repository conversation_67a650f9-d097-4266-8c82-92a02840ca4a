# QR-Code Based Student Attendance and Monitoring System

A comprehensive Node.js application for managing student attendance using QR codes, built with Express.js and SQLite.

## Features

- **QR Code Generation**: Automatic QR code generation for students and class sessions
- **Student Management**: Complete student registration and profile management
- **Attendance Tracking**: Real-time attendance recording via QR code scanning
- **User Authentication**: Secure login system with role-based access (<PERSON><PERSON>, Teacher, Staff)
- **SMS Notifications**: Parent notification system for attendance updates
- **Reporting**: Comprehensive attendance reports and analytics
- **File Upload**: Student photo management with secure file handling

## Technology Stack

- **Backend**: Node.js with Express.js
- **Database**: SQLite3 with proper indexing
- **Authentication**: bcrypt for password hashing
- **QR Codes**: qrcode library for generation
- **File Upload**: multer for handling file uploads
- **Date/Time**: moment.js for date manipulation
- **CORS**: Cross-origin resource sharing support

## Project Structure

```
qrsams/
├── database/
│   ├── connection.js       # Database connection module
│   ├── init-db.js         # Database initialization script
│   ├── schema.sql         # Database schema with all tables
│   └── qrsams.db          # SQLite database file (created after init)
├── models/
│   ├── User.js            # User model
│   ├── Student.js         # Student model
│   └── ...                # Other models (to be implemented)
├── routes/
│   └── ...                # API routes (to be implemented)
├── views/
│   └── ...                # Frontend views (to be implemented)
├── public/
│   ├── css/               # Stylesheets
│   ├── js/                # Client-side JavaScript
│   └── images/            # Static images
├── uploads/
│   └── student-photos/    # Student photo uploads
├── server.js              # Main server file
├── package.json           # Dependencies and scripts
└── README.md              # This file
```

## Database Schema

### Tables

1. **users** - System users (admin, teachers, staff)
2. **students** - Student information with QR codes
3. **subjects** - Academic subjects
4. **class_sessions** - Class sessions with time-limited QR codes
5. **attendance** - Attendance records
6. **sms_logs** - SMS notification logs

## Installation

1. **Clone or download the project**
   ```bash
   cd qrsams
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Initialize the database**
   ```bash
   npm run init-db
   ```

4. **Check database contents** (optional)
   ```bash
   npm run check-db
   ```

5. **Start the server**
   ```bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   ```

## Default Credentials

After running the database initialization, you can log in with:

- **Username**: admin
- **Password**: admin123

**⚠️ IMPORTANT**: Change the default password immediately in production!

## API Endpoints

### Health Check
- `GET /health` - Check server and database status

### Authentication (to be implemented)
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/profile` - Get user profile

### Students (to be implemented)
- `GET /api/students` - Get all students
- `POST /api/students` - Create new student
- `GET /api/students/:id` - Get student by ID
- `PUT /api/students/:id` - Update student
- `DELETE /api/students/:id` - Delete student

### Attendance (to be implemented)
- `POST /api/attendance/scan` - Record attendance via QR scan
- `GET /api/attendance/session/:sessionId` - Get session attendance
- `GET /api/attendance/student/:studentId` - Get student attendance history

## Development

### Running in Development Mode
```bash
npm run dev
```

This uses nodemon for automatic server restart on file changes.

### Database Management
```bash
# Initialize database with tables and sample data
npm run init-db

# Check database contents and display summary
npm run check-db

# Reinitialize database (WARNING: This will delete all data)
# Just delete the database file and run init-db again
```

## Configuration

### Environment Variables
Create a `.env` file for production configuration:
```
NODE_ENV=production
PORT=3000
DB_PATH=./database/qrsams.db
```

### File Upload Limits
- Maximum file size: 5MB
- Allowed formats: Images only (for student photos)
- Storage location: `uploads/student-photos/`

## Security Features

- Password hashing with bcrypt
- SQL injection prevention with parameterized queries
- File upload validation
- CORS configuration
- Foreign key constraints enabled

## Next Steps

1. Implement authentication routes
2. Create student management routes
3. Build attendance scanning functionality
4. Add SMS notification integration
5. Create frontend interface
6. Add comprehensive error handling
7. Implement data validation
8. Add unit tests

## License

MIT License
