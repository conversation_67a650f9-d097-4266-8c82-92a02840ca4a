{"name": "qrsams", "version": "1.0.0", "description": "QR-Code Based Student Attendance and Monitoring System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node database/init-db.js", "check-db": "node database/check-db.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["qr-code", "attendance", "student", "monitoring", "education", "express", "sqlite"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "qrcode": "^1.5.3", "bcrypt": "^5.1.1", "multer": "^1.4.5-lts.1", "moment": "^2.29.4", "cors": "^2.8.5", "body-parser": "^1.20.2", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.1"}}