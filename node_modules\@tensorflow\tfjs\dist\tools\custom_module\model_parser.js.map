{"version": 3, "file": "model_parser.js", "sourceRoot": "", "sources": ["../../../tools/custom_module/model_parser.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAKH,uBAAyB;AAEzB,+BAA4B;AAE5B,SAAgB,eAAe;AAC3B,mCAAmC;AACnC,MAA8B,EAAE,WAAgB;IAClD,sEAAsE;IACtE,IAAM,OAAO,GAAgB,IAAI,GAAG,EAAE,CAAC;IACvC,IAAI,SAAS,CAAC;IACd,KAA4B,UAAa,EAAb,KAAA,MAAM,CAAC,MAAM,EAAb,cAAa,EAAb,IAAa,EAAE;QAAtC,IAAM,aAAa,SAAA;QACtB,IAAI;YACF,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC;SACjE;QAAC,OAAO,CAAC,EAAE;YACV,IAAA,WAAI,EAAC,kCAA2B,aAAa,CAAE,CAAC,CAAC;SAClD;QAED,IAAM,GAAG,GAAG,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAC3C,GAAG,CAAC,OAAO,CAAC,UAAC,EAAU,IAAK,OAAA,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAf,CAAe,CAAC,CAAC;KAC9C;IACD,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AAjBD,0CAiBC;AAED,SAAgB,MAAM;AAClB,mCAAmC;AACnC,SAA4B,EAAE,UAAe;IAC/C,IAAM,OAAO,GAAgB,IAAI,GAAG,EAAE,CAAC;IAEvC,IAAM,eAAe,GAAG,UAAC,MAAc;QACrC,IAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,OAAO,CAAC,IAAI,CACR,qEACI,MAAM,CAAE,CAAC,CAAC;SACnB;QACD,GAAG,CAAC,OAAO,CAAC,UAAC,EAAU;YACrB,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,IAAM,KAAK,GAAG,SAAS,CAAC,aAAqC,CAAC;IAE9D,cAAc;IACd,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE;QACtB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,IAAI;YACtB,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;KACJ;IAED,0BAA0B;IAC1B,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;QAC3D,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,WAAW;YACzC,IAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;YACpC,IAAI,OAAO,IAAI,IAAI,EAAE;gBACnB,OAAO,CAAC,OAAO,CAAC,UAAC,IAAI;oBACnB,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AAvCD,wBAuCC"}